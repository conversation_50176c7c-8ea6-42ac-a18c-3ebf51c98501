import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { CustomerDTOWithModifiedAddress } from "../../../../scripts/customer-migration";
import { EXTENDED_CUSTOMER_MODULE } from "../../../../modules/extended/customer";
import ExtendedCustomerService from "../../../../modules/extended/customer/service";
import { CustomerDTO, InferTypeOf } from "@medusajs/framework/types";
import ExtendedCustomer from "../../../../modules/extended/customer/models/extended-customer";
import { Modules } from "@medusajs/framework/utils";
import { Customer } from "../../../../../.medusa/types/query-entry-points";

type Input = {
  customer_id : string,
  additional_data: any;
}

export const upsertExtendedCustomerStep = createStep(
  'upsert-extended-customer',
  async (input: Input, { container }) => {
    const extendedCustomerService = container.resolve<ExtendedCustomerService>(
      EXTENDED_CUSTOMER_MODULE
    );
    const logger = container.resolve('logger');
    const query = container.resolve('query')

    const {data : [customer]} = await query.graph({
      entity: 'customer',
      fields: ['*', 'extended_customer.*'],
      filters: { id: input.customer_id },
    })

    logger.info(`Upserting extended customer for customer_id: ${customer.id}`);
    const link = container.resolve('link')

    try {
      let extendedCustomer: any;
      let wasUpdate = false;
      let previousData: any = null;

      console.log('----> customer: ', customer);

      if (customer.extended_customer) {
        
        //delete the previous and create the new object.

        await extendedCustomerService.deleteExtendedCustomers(customer.extended_customer.id);

        await link.delete({
          [Modules.CUSTOMER]: { customer_id: customer.id },
        });

        extendedCustomer = await extendedCustomerService.createExtendedCustomers({
          ...input.additional_data
        });
        wasUpdate = true;
        previousData = customer.extended_customer;

      } else {
        
        extendedCustomer = await extendedCustomerService.createExtendedCustomers({
          ...input.additional_data
        });

      }

      logger.info(`Successfully upserted extended customer: ${extendedCustomer.id}`);

      return new StepResponse(extendedCustomer, {
        extendedCustomerId: extendedCustomer.id,
        wasUpdate,
        previousData
      });

    } catch (error) {
      logger.error(`Error upserting extended customer:`, error);
      throw error;
    }
  }
)