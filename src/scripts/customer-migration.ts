import { Link } from '@medusajs/framework/modules-sdk';
import { CustomerAddressDTO, CustomerDTO, ExecArgs, ICustomerModuleService, RemoteQueryFunction } from '@medusajs/framework/types';
import { ContainerRegistrationKeys } from '@medusajs/framework/utils';
import { createObjectCsvWriter } from 'csv-writer';
import { parse } from 'fast-csv';
import fs from 'fs';
import path from 'path';
import ExtendedCustomerService from '../modules/extended/customer/service';
import { createItemInDB } from './utils/customers';

export type CustomerDTOWithModifiedAddress<T extends keyof CustomerAddressDTO> = Omit<CustomerDTO, "addresses"> & {
    addresses: Omit<CustomerAddressDTO, T>[];
};

// 👇 Types
export interface CustomerCSVRow {
    'Customer ID': string;
    'Customer Name': string;
    'Mobile Number': string;
    'City': string;
    'Total Orders': string;
    'Total Sales (₹)': string;
    'Email': string;
    'Tags': string;
    'Loyalty Points (Perpetual Balance)': string;
    'Loyalty Points (Expirable Balance)': string;
}

export interface servicePack {
    customerService: ICustomerModuleService;
    extendedCustomerService: ExtendedCustomerService;
    link: Link;
    query: Omit<RemoteQueryFunction, symbol>
}

// 👇 Append logs to a file
const appendLog = (message: string) => {
    const logPath = path.join(__dirname, 'migration-log.txt');
    fs.appendFileSync(logPath, message + '\n');
};

// 👇 Validate function: maps CSV row to CustomerDTO
const validate = async (row: CustomerCSVRow): Promise<CustomerDTOWithModifiedAddress<'id'> & { additional_data: any }> => {
    const errors: string[] = [];

    if (!row['Customer ID']) errors.push('Missing Customer ID');
    if (!row['Mobile Number']) errors.push('Missing Phone Number');
    // if (!row['Customer Name']) errors.push('Missing Customer Name');

    const totalOrders = parseFloat(row['Total Orders'] || '0');
    if (isNaN(totalOrders)) errors.push('Invalid Total Orders');

    const totalSales = parseFloat(row['Total Sales (₹)'] || '0');
    if (isNaN(totalSales)) errors.push('Invalid Total Sales');

    const loyaltyPerpetual = Number(parseFloat(row['Loyalty Points (Perpetual Balance)'] || '0').toFixed(3));
    const loyaltyExpirable = Number(parseFloat(row['Loyalty Points (Expirable Balance)'] || '0').toFixed(3));

    const tags = row['Tags']
        ? row['Tags'].split(',').map(tag => tag.trim())
        : [];

    if (errors.length > 0) {
        throw new Error(errors.join('; '));
    }

    // Map to DTO
    const customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any } = {
        id: row['Customer ID'],
        email: row['Email'],
        has_account: true, // Assuming CSV customers have accounts
        default_billing_address_id: null,
        default_shipping_address_id: null,
        company_name: null,
        first_name: row['Customer Name'].split(' ')[0] || null,
        last_name: row['Customer Name'].split(' ').slice(1).join(' ') || null,
        addresses: row['City'] ? [{
            address_1: row['City'],
            city: row['City'],
            country_code: 'IN',
            postal_code: '',
            province: '',
            customer_id: row['Customer ID'],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            is_default_shipping: true,
            is_default_billing: true,
        }] : [],
        phone: row['Mobile Number'] || null,
        groups: tags.map((tag, i) => ({
            id: `tag_${i + 1}`,
            name: tag,
        })),
        additional_data: {
            city: row['City'],
            totalOrders,
            totalSales,
            non_expirable_loyalty_points: loyaltyPerpetual,
            expirable_loyalty_points: loyaltyExpirable,
        },
        metadata: {},
        created_by: 'migration-script',
        deleted_at: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
    };

    return customer;
};

// 👇 Main Migration Script
export default async function migrateCustomers({ container }: ExecArgs) {

    const customerService = container.resolve('customer');
    const extendedCustomerService = container.resolve('extended_customer');
    const linkService = container.resolve('link');
    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    const servicePack: servicePack = {
        customerService,
        extendedCustomerService,
        link: linkService,
        query,
    };

    const filePath = path.join(__dirname, 'migration-files', 'customers.csv');
    const errorFilePath = path.join(__dirname, 'migration-errors.csv');

    const errorCsvWriter = createObjectCsvWriter({
        path: errorFilePath,
        header: [
            { id: 'Customer ID', title: 'Customer ID' },
            { id: 'Error', title: 'Error' },
        ],
        append: false,
    });

    const rows: CustomerCSVRow[] = [];

    // Read CSV into rows[]
    await new Promise<void>((resolve, reject) => {
        fs.createReadStream(filePath)
            .pipe(parse({ headers: true }))
            .on('data', (row: CustomerCSVRow) => rows.push(row))
            .on('end', () => resolve())
            .on('error', err => reject(err));
    });

    console.log(`✅ Total rows to process: ${rows.length}`);
    appendLog(`Starting migration. Total rows: ${rows.length}`);

    const BATCH_SIZE = 10; // Adjust this based on your system

    for (let i = 0; i < 15; i += BATCH_SIZE) {
        const batch = rows.slice(i, i + BATCH_SIZE);
        await Promise.all(
            batch.map(async (row, index) => {
                const rowIndex = i + index + 1;
                try {
                    const validatedRow = await validate(row);
                    const msg = await createItemInDB(validatedRow, servicePack, container);
                    const successMsg = `Row ${rowIndex}: ${msg}`;
                    console.log(successMsg);
                    appendLog(successMsg);
                } catch (error) {
                    const errorMsg = error instanceof Error ? error.message : JSON.stringify(error);
                    const failMsg = `Row ${rowIndex}: ERROR - ${errorMsg}`;
                    console.error(failMsg);
                    appendLog(failMsg);
                    await errorCsvWriter.writeRecords([{ 'Customer ID': row['Customer ID'], Error: errorMsg }]);
                }
            })
        );
    }

    console.log('✅ Migration complete.');
    appendLog('Migration complete.');
}
